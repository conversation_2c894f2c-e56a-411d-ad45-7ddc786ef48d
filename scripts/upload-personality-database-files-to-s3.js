const dotenv = require("dotenv").config();
const mongoose = require("mongoose");
const { uploadCategoriesToFilesToS3 } = require('../lib/database')
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost/test";

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    console.log('Uploading categories from database to s3');
    await uploadCategoriesToFilesToS3();
    console.log('Finished uploading categories from database to s3');

    mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  } catch (error) {
    console.error("Error during backfill:", error);
  }
})();
